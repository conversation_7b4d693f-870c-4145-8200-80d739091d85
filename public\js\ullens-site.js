// Ullens School Site JavaScript
// Handles site-specific functionality like sliders, animations, and interactions

// Hero Slider Functionality
let currentSlide = 0;
const slides = document.querySelectorAll('.hero-slide');
const indicators = document.querySelectorAll('.indicator');

function showSlide(n) {
  // Hide all slides
  slides.forEach(slide => slide.classList.remove('active'));
  indicators.forEach(indicator => indicator.classList.remove('active'));
  
  // Wrap around if necessary
  if (n >= slides.length) currentSlide = 0;
  if (n < 0) currentSlide = slides.length - 1;
  
  // Show current slide
  if (slides[currentSlide]) {
    slides[currentSlide].classList.add('active');
  }
  if (indicators[currentSlide]) {
    indicators[currentSlide].classList.add('active');
  }
}

function changeSlide(direction) {
  currentSlide += direction;
  showSlide(currentSlide);
}

function currentSlideSet(n) {
  currentSlide = n - 1;
  showSlide(currentSlide);
}

// Auto-advance slides
function autoSlide() {
  currentSlide++;
  showSlide(currentSlide);
}

// Start auto-slide if slides exist
if (slides.length > 0) {
  setInterval(autoSlide, 5000); // Change slide every 5 seconds
}

// Testimonials Slider
let currentTestimonial = 0;
const testimonials = document.querySelectorAll('.testimonial-card');

function showTestimonial(n) {
  testimonials.forEach(testimonial => testimonial.classList.remove('active'));
  
  if (n >= testimonials.length) currentTestimonial = 0;
  if (n < 0) currentTestimonial = testimonials.length - 1;
  
  if (testimonials[currentTestimonial]) {
    testimonials[currentTestimonial].classList.add('active');
  }
}

function changeTestimonial(direction) {
  currentTestimonial += direction;
  showTestimonial(currentTestimonial);
}

// Auto-advance testimonials
if (testimonials.length > 0) {
  setInterval(() => {
    currentTestimonial++;
    showTestimonial(currentTestimonial);
  }, 7000); // Change testimonial every 7 seconds
}

// Smooth scrolling for navigation links
document.addEventListener('DOMContentLoaded', () => {
  // Handle navigation clicks
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        const headerHeight = document.querySelector('.ullens-header').offsetHeight;
        const targetPosition = target.offsetTop - headerHeight - 20;
        
        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });
      }
    });
  });

  // Contact form handling
  const contactForm = document.getElementById('contact-form');
  if (contactForm) {
    contactForm.addEventListener('submit', function(e) {
      e.preventDefault();
      
      // Get form data
      const formData = new FormData(this);
      const formObject = {};
      formData.forEach((value, key) => {
        formObject[key] = value;
      });
      
      // Show success message (in a real implementation, you'd send this to a server)
      alert('Thank you for your message! We will get back to you soon.');
      this.reset();
    });
  }

  // Add scroll effect to header
  const header = document.querySelector('.ullens-header');
  if (header) {
    window.addEventListener('scroll', () => {
      if (window.scrollY > 100) {
        header.style.background = 'rgba(30, 58, 138, 0.95)';
        header.style.backdropFilter = 'blur(10px)';
      } else {
        header.style.background = 'linear-gradient(135deg, var(--ullens-primary) 0%, var(--ullens-dark-blue) 100%)';
        header.style.backdropFilter = 'none';
      }
    });
  }

  // Animate elements on scroll
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observe elements for animation
  document.querySelectorAll('.program-card, .testimonial-card, .info-card').forEach(el => {
    el.style.opacity = '0';
    el.style.transform = 'translateY(30px)';
    el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(el);
  });

  // Add hover effects to program cards
  document.querySelectorAll('.program-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-10px) scale(1.02)';
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0) scale(1)';
    });
  });

  // Mobile menu toggle (if needed)
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const navMenu = document.querySelector('.nav-menu');
  
  if (mobileMenuToggle && navMenu) {
    mobileMenuToggle.addEventListener('click', () => {
      navMenu.classList.toggle('active');
    });
  }

  // Add loading animation to buttons
  document.querySelectorAll('.btn').forEach(btn => {
    btn.addEventListener('click', function(e) {
      // Don't add loading to navigation links
      if (this.getAttribute('href') && this.getAttribute('href').startsWith('#')) {
        return;
      }
      
      // Add ripple effect
      const ripple = document.createElement('span');
      const rect = this.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;
      
      ripple.style.width = ripple.style.height = size + 'px';
      ripple.style.left = x + 'px';
      ripple.style.top = y + 'px';
      ripple.classList.add('ripple');
      
      this.appendChild(ripple);
      
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });

  // Add parallax effect to hero section
  const heroSlider = document.querySelector('.hero-slider');
  if (heroSlider) {
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;
      const parallax = scrolled * 0.5;
      heroSlider.style.transform = `translateY(${parallax}px)`;
    });
  }

  // Initialize counters for stats
  const statItems = document.querySelectorAll('.stat-item h3');
  const statsObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const target = entry.target;
        const finalValue = target.textContent;
        
        // Only animate if it's a number
        if (!isNaN(finalValue)) {
          animateCounter(target, 0, parseInt(finalValue), 2000);
        }
        
        statsObserver.unobserve(target);
      }
    });
  });

  statItems.forEach(item => {
    statsObserver.observe(item);
  });

  console.log('🎓 Ullens School Site JavaScript Loaded');
});

// Counter animation function
function animateCounter(element, start, end, duration) {
  const startTime = performance.now();
  
  function updateCounter(currentTime) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    
    const current = Math.floor(start + (end - start) * progress);
    element.textContent = current;
    
    if (progress < 1) {
      requestAnimationFrame(updateCounter);
    } else {
      element.textContent = end;
    }
  }
  
  requestAnimationFrame(updateCounter);
}

// Add ripple effect CSS
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
  .btn {
    position: relative;
    overflow: hidden;
  }
  
  .ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
  }
  
  @keyframes ripple-animation {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }
`;
document.head.appendChild(rippleStyle);

// Export functions for global use
window.changeSlide = changeSlide;
window.currentSlide = currentSlideSet;
window.changeTestimonial = changeTestimonial;
