# 🌐 Ullens Authentication System

A secure backend web application with user authentication functionality, featuring the visual theme and styling from Ullens School (ullens.edu.np). This system provides a complete authentication solution with a beautiful, responsive frontend that matches the Ullens School aesthetic.

## 🎯 Project Goals

- Implement secure user authentication (register, login, logout, password reset)
- Create a beautiful frontend interface matching Ullens School's visual design
- Provide JWT-based session management
- Ensure secure password handling and user data protection
- Build responsive, accessible user interfaces
- Include comprehensive error handling and validation

---

## 🧱 Features

### ✅ Authentication System
- **User Registration**: Secure signup with email validation
- **User Login**: JWT token-based authentication
- **Password Reset**: Secure password recovery via email
- **Session Management**: Automatic token refresh and logout
- **Protected Routes**: Middleware-based route protection

### ✅ Security Features
- **Password Hashing**: bcrypt encryption for secure password storage
- **JWT Tokens**: Stateless authentication with configurable expiration
- **Input Validation**: Comprehensive data validation and sanitization
- **Rate Limiting**: Protection against brute force attacks
- **CORS Configuration**: Secure cross-origin resource sharing

### ✅ Ullens School Themed UI
- **Color Scheme**: Blue and orange branding matching Ullens School
- **Typography**: Clean, professional fonts and text styling
- **Responsive Design**: Mobile-first approach with responsive layouts
- **Form Design**: Beautiful, accessible forms with proper validation feedback
- **Navigation**: Intuitive navigation matching Ullens School patterns

### ✅ API Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/reset-password` - Password reset request
- `GET /api/auth/profile` - Get user profile (protected)
- `PUT /api/auth/profile` - Update user profile (protected)

---

## ⚙️ Tech Stack

- **Backend**: Node.js + Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Password Security**: bcrypt for hashing
- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Styling**: Custom CSS inspired by Ullens School design
- **Validation**: express-validator for input validation

---

## 🚀 Getting Started

### 📦 Prerequisites

- Node.js (v14 or higher)
- MongoDB (local or cloud instance)
- npm or yarn package manager

### 📦 Installation

```bash
# Clone the repository
git clone <repository-url>
cd demo_website

# Install dependencies
npm install

# Create environment file
cp .env.example .env

# Configure your environment variables
# Edit .env with your database URL, JWT secret, etc.

# Start the development server
npm run dev

# Access the application
# Main website: http://localhost:3000
# Student Portal: http://localhost:3000/login
# Registration: http://localhost:3000/register
# Dashboard: http://localhost:3000/dashboard

---

## 🎨 Demo Features

### 🏠 Main Website
- **Ullens School Design**: Faithful recreation of ullens.edu.np visual design
- **Hero Slider**: Rotating banners with call-to-action buttons
- **Programs Section**: Comprehensive overview of educational programs
- **Testimonials**: Student voices and experiences
- **Admissions Information**: Application process and requirements
- **Contact Section**: School contact details and inquiry form

### 🔐 Authentication System
- **Student Portal**: Secure login for students and staff
- **Registration**: New user account creation
- **Dashboard**: Protected user dashboard with profile management
- **Admin Panel**: Administrative functions for user management

### 📱 Responsive Design
- **Mobile-First**: Optimized for all device sizes
- **Touch-Friendly**: Intuitive navigation and interactions
- **Fast Loading**: Optimized assets and efficient code

---

## 🧪 Demo Credentials

For testing the authentication system, use these demo accounts:

### Admin Account
- **Email**: <EMAIL>
- **Password**: password123
- **Role**: Administrator with full access

### Regular User Account
- **Email**: <EMAIL>
- **Password**: password123
- **Role**: Regular user with standard access

### Create New Account
You can also register a new account using the registration form with:
- Valid email address
- Strong password (8+ chars, uppercase, lowercase, number, special character)
- First and last name

---

## 🛠️ Technical Features

### Backend API
- **RESTful Design**: Clean, consistent API endpoints
- **JWT Authentication**: Secure token-based authentication
- **Input Validation**: Comprehensive data validation
- **Rate Limiting**: Protection against abuse
- **Error Handling**: Graceful error responses

### Frontend Features
- **Modern JavaScript**: ES6+ features and best practices
- **CSS Grid & Flexbox**: Modern layout techniques
- **Smooth Animations**: CSS transitions and JavaScript animations
- **Form Validation**: Real-time client-side validation
- **Responsive Images**: Optimized for different screen sizes

### Security Features
- **Password Hashing**: bcrypt encryption
- **Account Lockout**: Protection against brute force attacks
- **Session Management**: Secure token handling
- **CORS Protection**: Cross-origin request security
- **Input Sanitization**: XSS and injection prevention
