# 🌐 Ullens Central Management Console (Backend)

This is a multi-tenant, role-based, headless CMS built for the Ullens ecosystem. It serves structured JSON content for multiple Ullens verticals such as Ullens School Khumaltar, Ullens College, Ullens Foundation, and more.

## 🎯 Project Goals

- Define and manage multiple websites (tenants)
- Manage pages and key-value content blocks for each site
- Serve content via a secure JSON API to static frontends
- Enforce IP and API key restrictions per site
- Implement per-site Role-Based Access Control (RBAC)
- Support content types like text, images, galleries, buttons, and more

---

## 🧱 Features

### ✅ Multi-Site Architecture
- Each site has a unique key and IP whitelist
- Independent pages and content for each site

### ✅ Structured Content Management
- Pages: Slug-based structure (e.g. `/about-us`)
- Key-value content: Paragraphs, images, buttons, HTML, lists
- Media support: Upload, tag, manage files per site

### ✅ Role-Based Access Control
- **Editor**: Create and edit content
- **Reviewer**: Review and publish content
- **Admin**: Full control over one site
- **Superuser**: Control over all sites

### ✅ API Endpoint
- `GET /api/content/?key=<site_key>&page=<page_slug>`
- Returns JSON payload with site-specific content
- Secured by IP and API key validation

### ✅ Admin Dashboard
- Rich content editing (WYSIWYG, image uploads, tagging)
- Review workflows and publish status
- Activity logs and audit trails

---

## ⚙️ Tech Stack

- **Backend**: Django + Django Rest Framework (DRF)
- **Database**: PostgreSQL / MySQL
- **Media Handling**: Django Media Files, optional S3/CDN
- **Auth**: Token-based + IP filtering middleware
- **Admin UI**: Django Admin or custom frontend

---

## 🚀 Getting Started

### 📦 Clone and Install

```bash
git clone https://github.com/ullens/central-management-console.git
cd central-management-console
python3 -m venv env
source env/bin/activate
pip install -r requirements.txt
