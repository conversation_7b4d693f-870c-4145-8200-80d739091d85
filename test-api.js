// Simple API test script for Ullens Authentication System
const http = require('http');

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({ status: res.statusCode, data: jsonBody });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

// Test functions
async function testHealthCheck() {
  console.log('🔍 Testing Health Check...');
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/health',
      method: 'GET'
    });
    
    console.log(`✅ Health Check: ${response.status}`);
    console.log(`📊 Response:`, response.data);
    return response.status === 200;
  } catch (error) {
    console.log('❌ Health Check failed:', error.message);
    return false;
  }
}

async function testLogin() {
  console.log('\n🔍 Testing Login with demo user...');
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    console.log(`✅ Login Test: ${response.status}`);
    console.log(`📊 Response:`, response.data);
    
    if (response.data.token) {
      console.log(`🔑 Token received: ${response.data.token.substring(0, 20)}...`);
      return response.data.token;
    }
    return null;
  } catch (error) {
    console.log('❌ Login test failed:', error.message);
    return null;
  }
}

async function testProtectedRoute(token) {
  console.log('\n🔍 Testing Protected Route...');
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/protected/dashboard',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`✅ Protected Route Test: ${response.status}`);
    console.log(`📊 Response:`, response.data);
    return response.status === 200;
  } catch (error) {
    console.log('❌ Protected route test failed:', error.message);
    return false;
  }
}

async function testRegistration() {
  console.log('\n🔍 Testing User Registration...');
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/auth/register',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }, {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });
    
    console.log(`✅ Registration Test: ${response.status}`);
    console.log(`📊 Response:`, response.data);
    return response.status === 201;
  } catch (error) {
    console.log('❌ Registration test failed:', error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('🎓 Ullens Authentication System - API Tests\n');
  console.log('=' .repeat(50));
  
  const healthOk = await testHealthCheck();
  if (!healthOk) {
    console.log('❌ Server is not responding. Please check if the server is running.');
    return;
  }
  
  const token = await testLogin();
  if (token) {
    await testProtectedRoute(token);
  }
  
  await testRegistration();
  
  console.log('\n' + '='.repeat(50));
  console.log('🎉 API Tests Completed!');
  console.log('💡 You can now test the frontend by visiting: http://localhost:3000');
  console.log('🔑 Demo login credentials:');
  console.log('   Email: <EMAIL>');
  console.log('   Password: password123');
}

// Run the tests
runTests().catch(console.error);
