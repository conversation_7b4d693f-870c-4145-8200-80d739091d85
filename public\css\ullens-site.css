/* Ullens School Site-Specific Styles */
/* Additional styles for the main Ullens School website */

/* Header Styles */
.ullens-header {
  background: linear-gradient(135deg, var(--ullens-primary) 0%, var(--ullens-dark-blue) 100%);
  color: var(--ullens-white);
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ullens-logo h1 {
  color: var(--ullens-white);
  font-size: 2rem;
  margin-bottom: var(--spacing-xs);
}

.tagline {
  color: var(--ullens-secondary);
  font-size: 0.9rem;
  font-style: italic;
  margin: 0;
}

.auth-buttons {
  display: flex;
  gap: var(--spacing-md);
}

.btn-outline-light {
  background: transparent;
  color: var(--ullens-white);
  border: 2px solid var(--ullens-white);
}

.btn-outline-light:hover {
  background: var(--ullens-white);
  color: var(--ullens-primary);
}

/* Navigation Styles */
.main-nav {
  padding: var(--spacing-md) 0;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-link {
  color: var(--ullens-white);
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--ullens-white);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--ullens-white);
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-md);
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  list-style: none;
  padding: var(--spacing-sm) 0;
  margin: 0;
  z-index: 1001;
}

.nav-item:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu li a {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  color: var(--ullens-primary);
  transition: background-color 0.3s ease;
}

.dropdown-menu li a:hover {
  background: var(--ullens-light-gray);
}

/* Hero Slider */
.hero-slider {
  position: relative;
  height: 70vh;
  overflow: hidden;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  display: flex;
  align-items: center;
}

.hero-slide.active {
  opacity: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
}

.hero-text {
  max-width: 600px;
  color: var(--ullens-white);
}

.hero-text h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  color: var(--ullens-white);
  line-height: 1.2;
}

.hero-text p {
  font-size: 1.2rem;
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.btn-large {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: 1.1rem;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--ullens-primary) 0%, var(--ullens-dark-blue) 100%);
  z-index: 1;
}

.hero-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.ibdp-bg {
  background: linear-gradient(135deg, var(--ullens-secondary) 0%, #ea580c 100%);
}

.hero-navigation {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  z-index: 3;
}

.hero-nav-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: var(--ullens-white);
  font-size: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.hero-nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.hero-indicators {
  position: absolute;
  bottom: var(--spacing-lg);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--spacing-sm);
  z-index: 3;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: var(--ullens-white);
  transform: scale(1.2);
}

/* Main Content */
.main-content {
  padding: 0;
}

.section-header {
  margin-bottom: var(--spacing-2xl);
}

.section-header h2 {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
}

.section-subtitle {
  font-size: 1.2rem;
  color: var(--ullens-gray);
  margin: 0;
}

/* Welcome Section */
.welcome-section {
  padding: var(--spacing-2xl) 0;
  background: var(--ullens-white);
}

.welcome-content {
  margin-top: var(--spacing-xl);
}

.welcome-text em {
  color: var(--ullens-secondary);
  font-style: italic;
}

.welcome-stats {
  display: flex;
  gap: var(--spacing-xl);
  margin: var(--spacing-xl) 0;
}

.stat-item {
  text-align: center;
}

.stat-item h3 {
  font-size: 2rem;
  color: var(--ullens-secondary);
  margin-bottom: var(--spacing-xs);
}

.stat-item p {
  color: var(--ullens-gray);
  font-size: 0.9rem;
  margin: 0;
}

.welcome-image {
  padding-left: var(--spacing-lg);
}

.image-placeholder {
  background: var(--ullens-light-gray);
  border-radius: var(--radius-lg);
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed var(--ullens-gray);
}

.placeholder-content {
  text-align: center;
  color: var(--ullens-gray);
}

.placeholder-content h4 {
  color: var(--ullens-primary);
  margin-bottom: var(--spacing-sm);
}

/* Programs Section */
.programs-section {
  padding: var(--spacing-2xl) 0;
  background: var(--ullens-light-gray);
}

.programs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.program-card {
  background: var(--ullens-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.program-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  border-color: var(--ullens-light-blue);
}

.program-card.featured {
  border-color: var(--ullens-secondary);
  background: linear-gradient(135deg, #fff 0%, #fef3e2 100%);
}

.program-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
}

.program-card h3 {
  margin-bottom: var(--spacing-md);
  color: var(--ullens-primary);
}

.program-features {
  list-style: none;
  padding: 0;
  margin: var(--spacing-lg) 0;
}

.program-features li {
  padding: var(--spacing-xs) 0;
  color: var(--ullens-gray);
  position: relative;
  padding-left: var(--spacing-lg);
}

.program-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--ullens-success);
  font-weight: bold;
}

.ib-logo {
  background: var(--ullens-secondary);
  color: var(--ullens-white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.8rem;
  font-weight: bold;
  margin: var(--spacing-md) 0;
  display: inline-block;
}

/* Foundation Section */
.foundation-section {
  padding: var(--spacing-2xl) 0;
  background: var(--ullens-white);
}

.foundation-highlights {
  margin: var(--spacing-xl) 0;
}

.highlight-item {
  margin-bottom: var(--spacing-lg);
}

.highlight-item h4 {
  color: var(--ullens-secondary);
  margin-bottom: var(--spacing-sm);
}

.foundation-image {
  padding-left: var(--spacing-lg);
}

/* Testimonials Section */
.testimonials-section {
  padding: var(--spacing-2xl) 0;
  background: var(--ullens-primary);
  color: var(--ullens-white);
}

.testimonials-section .section-header h2,
.testimonials-section .section-subtitle {
  color: var(--ullens-white);
}

.testimonials-slider {
  position: relative;
  max-width: 800px;
  margin: var(--spacing-xl) auto 0;
}

.testimonial-card {
  display: none;
  text-align: center;
  padding: var(--spacing-xl);
}

.testimonial-card.active {
  display: block;
}

.quote-icon {
  font-size: 4rem;
  color: var(--ullens-secondary);
  margin-bottom: var(--spacing-md);
}

.testimonial-content p {
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: var(--spacing-xl);
  font-style: italic;
}

.testimonial-author h4 {
  color: var(--ullens-white);
  margin-bottom: var(--spacing-xs);
}

.testimonial-author p {
  color: var(--ullens-secondary);
  font-size: 0.9rem;
  margin: 0;
}

.testimonial-navigation {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

.testimonial-nav {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: var(--ullens-white);
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.testimonial-nav:hover {
  background: var(--ullens-secondary);
}

/* Admissions Section */
.admissions-section {
  padding: var(--spacing-2xl) 0;
  background: var(--ullens-light-gray);
}

.admissions-content h2 {
  font-size: 2.2rem;
  margin-bottom: var(--spacing-md);
}

.admissions-content h3 {
  color: var(--ullens-secondary);
  font-size: 1.8rem;
  margin-bottom: var(--spacing-md);
}

.admissions-process {
  margin: var(--spacing-xl) 0;
}

.process-step {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  gap: var(--spacing-md);
}

.step-number {
  background: var(--ullens-secondary);
  color: var(--ullens-white);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content h4 {
  margin-bottom: var(--spacing-xs);
  color: var(--ullens-primary);
}

.step-content p {
  margin: 0;
  color: var(--ullens-gray);
}

.admissions-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
}

.admissions-info {
  padding-left: var(--spacing-lg);
}

.info-card {
  background: var(--ullens-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-md);
}

.info-card h4 {
  color: var(--ullens-primary);
  margin-bottom: var(--spacing-md);
}

.info-card ul {
  list-style: none;
  padding: 0;
}

.info-card li {
  padding: var(--spacing-xs) 0;
  color: var(--ullens-gray);
  position: relative;
  padding-left: var(--spacing-lg);
}

.info-card li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--ullens-secondary);
  font-weight: bold;
}

/* Contact Section */
.contact-section {
  padding: var(--spacing-2xl) 0;
  background: var(--ullens-white);
}

.contact-info {
  padding-right: var(--spacing-lg);
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
  gap: var(--spacing-md);
}

.contact-icon {
  font-size: 1.5rem;
  width: 40px;
  text-align: center;
  flex-shrink: 0;
}

.contact-details h4 {
  color: var(--ullens-primary);
  margin-bottom: var(--spacing-sm);
}

.contact-details p {
  color: var(--ullens-gray);
  margin: 0;
  line-height: 1.6;
}

.contact-form {
  background: var(--ullens-light-gray);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
}

.contact-form h3 {
  color: var(--ullens-primary);
  margin-bottom: var(--spacing-lg);
}

/* Footer */
.ullens-footer {
  background: var(--ullens-primary);
  color: var(--ullens-white);
  padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.footer-section h3,
.footer-section h4 {
  color: var(--ullens-white);
  margin-bottom: var(--spacing-md);
}

.footer-section h3 {
  color: var(--ullens-secondary);
  font-size: 1.2rem;
}

.footer-logo h2 {
  color: var(--ullens-white);
  font-size: 1.8rem;
  margin-bottom: var(--spacing-sm);
}

.footer-logo p {
  color: var(--ullens-secondary);
  font-size: 0.9rem;
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--ullens-secondary);
}

.contact-details p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: var(--spacing-xs);
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.social-link {
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.social-link:hover {
  color: var(--ullens-secondary);
}

.student-portal h5 {
  color: var(--ullens-white);
  margin-bottom: var(--spacing-sm);
}

.footer-partners {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: var(--spacing-lg) 0;
  margin-bottom: var(--spacing-lg);
}

.partner-logos {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
  flex-wrap: wrap;
}

.partner-logo {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-size: 0.8rem;
  font-weight: bold;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: var(--spacing-lg);
  text-align: center;
}

.footer-bottom p {
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: var(--spacing-xs);
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-top {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .nav-menu {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .hero-text h1 {
    font-size: 2rem;
  }

  .hero-actions {
    justify-content: center;
  }

  .welcome-stats {
    justify-content: center;
    flex-wrap: wrap;
  }

  .welcome-image,
  .foundation-image,
  .admissions-info,
  .contact-info {
    padding-left: 0;
    margin-top: var(--spacing-lg);
  }

  .programs-grid {
    grid-template-columns: 1fr;
  }

  .admissions-actions {
    flex-direction: column;
  }

  .partner-logos {
    gap: var(--spacing-md);
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

/* Utility Classes */
.col-md-6 {
  flex: 0 0 50%;
  max-width: 50%;
}

@media (max-width: 768px) {
  .col-md-6 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
