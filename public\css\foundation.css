/* Ullens Education Foundation Specific Styles */
/* Additional styles for the UEF website */

/* Foundation Header */
.foundation-header {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #f97316 100%);
  color: var(--ullens-white);
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.foundation-logo h1 {
  color: var(--ullens-white);
  font-size: 1.8rem;
  margin-bottom: var(--spacing-xs);
  font-weight: 700;
}

.foundation-logo .tagline {
  color: #fbbf24;
  font-size: 0.85rem;
  font-style: italic;
  margin: 0;
  opacity: 0.9;
}

/* Foundation Hero */
.foundation-hero {
  position: relative;
  min-height: 80vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--ullens-primary) 0%, var(--ullens-dark-blue) 50%, var(--ullens-secondary) 100%);
  z-index: 1;
}

.hero-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="foundation-grid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23foundation-grid)"/></svg>');
  opacity: 0.3;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
}

.hero-content {
  position: relative;
  z-index: 2;
  width: 100%;
}

.hero-text {
  max-width: 800px;
  color: var(--ullens-white);
  text-align: center;
  margin: 0 auto;
}

.hero-text h1 {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: var(--spacing-lg);
  color: var(--ullens-white);
  line-height: 1.1;
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: var(--spacing-2xl);
  opacity: 0.95;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
  flex-wrap: wrap;
}

.stat-box {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 150px;
}

.stat-box h3 {
  font-size: 2.5rem;
  color: #fbbf24;
  margin-bottom: var(--spacing-xs);
  font-weight: 800;
}

.stat-box p {
  color: var(--ullens-white);
  font-size: 0.9rem;
  margin: 0;
  opacity: 0.9;
}

/* Foundation Content */
.foundation-content {
  padding: 0;
}

/* About Section */
.about-section {
  padding: var(--spacing-2xl) 0;
  background: var(--ullens-white);
}

.about-highlights {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.highlight-card {
  background: var(--ullens-light-gray);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--ullens-secondary);
  transition: all 0.3s ease;
}

.highlight-card:hover {
  transform: translateX(5px);
  box-shadow: var(--shadow-md);
}

.highlight-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
}

.highlight-card h4 {
  color: var(--ullens-primary);
  margin-bottom: var(--spacing-sm);
}

.highlight-card p {
  color: var(--ullens-gray);
  margin: 0;
  line-height: 1.6;
}

/* Programs Section */
.programs-section {
  padding: var(--spacing-2xl) 0;
  background: var(--ullens-light-gray);
}

.programs-showcase {
  margin-top: var(--spacing-xl);
}

.program-highlight {
  background: var(--ullens-white);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(30, 58, 138, 0.1);
}

.program-highlight.reverse .row {
  flex-direction: row-reverse;
}

.program-content h3 {
  font-size: 2rem;
  color: var(--ullens-primary);
  margin-bottom: var(--spacing-sm);
}

.program-subtitle {
  color: var(--ullens-secondary);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
}

.program-features {
  margin: var(--spacing-lg) 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  background: var(--ullens-light-gray);
  border-radius: var(--radius-md);
}

.feature-icon {
  font-size: 1.2rem;
  width: 30px;
  text-align: center;
}

.program-image {
  padding-left: var(--spacing-lg);
}

.program-highlight.reverse .program-image {
  padding-left: 0;
  padding-right: var(--spacing-lg);
}

.school-image {
  background: linear-gradient(135deg, var(--ullens-primary) 0%, var(--ullens-light-blue) 100%);
}

.kindergarten-image {
  background: linear-gradient(135deg, var(--ullens-secondary) 0%, #fbbf24 100%);
}

.school-image .placeholder-content,
.kindergarten-image .placeholder-content {
  color: var(--ullens-white);
}

/* Contact Section */
.contact-section {
  padding: var(--spacing-2xl) 0;
  background: var(--ullens-white);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2xl);
  margin-top: var(--spacing-xl);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.contact-icon {
  font-size: 1.5rem;
  width: 40px;
  text-align: center;
  flex-shrink: 0;
  color: var(--ullens-secondary);
}

.contact-details h4 {
  color: var(--ullens-primary);
  margin-bottom: var(--spacing-sm);
}

.contact-details p {
  color: var(--ullens-gray);
  margin: 0;
  line-height: 1.6;
}

.contact-form {
  background: var(--ullens-light-gray);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-lg);
}

.contact-form h3 {
  color: var(--ullens-primary);
  margin-bottom: var(--spacing-lg);
}

/* Foundation Footer */
.foundation-footer {
  background: var(--ullens-primary);
  color: var(--ullens-white);
  padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.footer-section h3 {
  color: #fbbf24;
  font-size: 1.3rem;
  margin-bottom: var(--spacing-md);
}

.footer-section h4 {
  color: var(--ullens-white);
  margin-bottom: var(--spacing-md);
}

.footer-section p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: var(--spacing-sm);
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #fbbf24;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: var(--spacing-lg);
  text-align: center;
}

.footer-bottom p {
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .foundation-logo h1 {
    font-size: 1.5rem;
  }
  
  .foundation-logo .tagline {
    font-size: 0.8rem;
  }
  
  .hero-text h1 {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .hero-stats {
    gap: var(--spacing-lg);
  }
  
  .stat-box {
    min-width: 120px;
    padding: var(--spacing-md);
  }
  
  .stat-box h3 {
    font-size: 2rem;
  }
  
  .about-highlights {
    margin-top: var(--spacing-lg);
  }
  
  .program-highlight {
    padding: var(--spacing-lg);
  }
  
  .program-image,
  .program-highlight.reverse .program-image {
    padding-left: 0;
    padding-right: 0;
    margin-top: var(--spacing-lg);
  }
  
  .program-highlight.reverse .row {
    flex-direction: column;
  }
  
  .contact-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .contact-form {
    padding: var(--spacing-lg);
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

/* Animation Classes */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-in-right {
  opacity: 0;
  transform: translateX(50px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.slide-in-right.visible {
  opacity: 1;
  transform: translateX(0);
}
