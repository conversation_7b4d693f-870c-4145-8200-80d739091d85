// Ullens Education Foundation JavaScript
// Handles foundation-specific functionality and interactions

document.addEventListener('DOMContentLoaded', () => {
  console.log('🏛️ Ullens Education Foundation JavaScript Loaded');

  // Initialize animations
  initializeAnimations();
  
  // Initialize counters
  initializeCounters();
  
  // Handle form submissions
  initializeForms();
  
  // Initialize scroll effects
  initializeScrollEffects();
});

// Animation initialization
function initializeAnimations() {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const animationObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible');
      }
    });
  }, observerOptions);

  // Add animation classes and observe elements
  document.querySelectorAll('.highlight-card').forEach((el, index) => {
    el.classList.add('fade-in');
    el.style.transitionDelay = `${index * 0.1}s`;
    animationObserver.observe(el);
  });

  document.querySelectorAll('.program-highlight').forEach((el, index) => {
    if (el.classList.contains('reverse')) {
      el.classList.add('slide-in-right');
    } else {
      el.classList.add('slide-in-left');
    }
    el.style.transitionDelay = `${index * 0.2}s`;
    animationObserver.observe(el);
  });

  document.querySelectorAll('.feature-item').forEach((el, index) => {
    el.classList.add('fade-in');
    el.style.transitionDelay = `${index * 0.05}s`;
    animationObserver.observe(el);
  });
}

// Counter animation for statistics
function initializeCounters() {
  const counterElements = document.querySelectorAll('.stat-box h3, .metric-number');
  
  const counterObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const target = entry.target;
        const text = target.textContent;
        
        // Extract number from text
        const numberMatch = text.match(/\d+/);
        if (numberMatch) {
          const finalNumber = parseInt(numberMatch[0]);
          const suffix = text.replace(numberMatch[0], '');
          
          animateCounter(target, 0, finalNumber, 2000, suffix);
        }
        
        counterObserver.unobserve(target);
      }
    });
  }, { threshold: 0.5 });

  counterElements.forEach(el => {
    counterObserver.observe(el);
  });
}

// Counter animation function
function animateCounter(element, start, end, duration, suffix = '') {
  const startTime = performance.now();
  const originalText = element.textContent;
  
  function updateCounter(currentTime) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);
    
    // Use easing function for smooth animation
    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
    const current = Math.floor(start + (end - start) * easeOutQuart);
    
    element.textContent = current + suffix;
    
    if (progress < 1) {
      requestAnimationFrame(updateCounter);
    } else {
      element.textContent = originalText; // Restore original text
    }
  }
  
  requestAnimationFrame(updateCounter);
}

// Form handling
function initializeForms() {
  // Contact form
  const contactForm = document.getElementById('contact-form');
  if (contactForm) {
    contactForm.addEventListener('submit', handleContactForm);
  }

  // Support form
  const supportForm = document.getElementById('support-form');
  if (supportForm) {
    supportForm.addEventListener('submit', handleSupportForm);
  }
}

function handleContactForm(e) {
  e.preventDefault();
  
  const formData = new FormData(e.target);
  const data = Object.fromEntries(formData);
  
  // Simulate form submission
  showFormSuccess('Thank you for your message! We will get back to you soon.');
  e.target.reset();
}

function handleSupportForm(e) {
  e.preventDefault();
  
  const formData = new FormData(e.target);
  const data = Object.fromEntries(formData);
  
  // Simulate form submission
  showFormSuccess('Thank you for your interest in supporting UEF! We will contact you shortly.');
  e.target.reset();
}

function showFormSuccess(message) {
  // Create success message
  const successDiv = document.createElement('div');
  successDiv.className = 'alert alert-success';
  successDiv.innerHTML = `
    <strong>Success!</strong> ${message}
    <button type="button" style="float: right; background: none; border: none; font-size: 1.2rem; cursor: pointer;" onclick="this.parentElement.remove()">×</button>
  `;
  
  // Insert at top of page
  document.body.insertBefore(successDiv, document.body.firstChild);
  
  // Auto-remove after 5 seconds
  setTimeout(() => {
    if (successDiv.parentElement) {
      successDiv.remove();
    }
  }, 5000);
  
  // Scroll to top to show message
  window.scrollTo({ top: 0, behavior: 'smooth' });
}

// Scroll effects
function initializeScrollEffects() {
  const header = document.querySelector('.foundation-header');
  
  if (header) {
    let lastScrollY = window.scrollY;
    
    window.addEventListener('scroll', () => {
      const currentScrollY = window.scrollY;
      
      // Header background opacity based on scroll
      if (currentScrollY > 100) {
        header.style.background = 'rgba(30, 58, 138, 0.95)';
        header.style.backdropFilter = 'blur(10px)';
      } else {
        header.style.background = 'linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #f97316 100%)';
        header.style.backdropFilter = 'none';
      }
      
      lastScrollY = currentScrollY;
    });
  }

  // Parallax effect for hero section
  const hero = document.querySelector('.foundation-hero');
  if (hero) {
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;
      const parallax = scrolled * 0.3;
      hero.style.transform = `translateY(${parallax}px)`;
    });
  }
}

// Smooth scrolling for anchor links
document.addEventListener('DOMContentLoaded', () => {
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        const headerHeight = document.querySelector('.foundation-header').offsetHeight;
        const targetPosition = target.offsetTop - headerHeight - 20;
        
        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        });
      }
    });
  });
});

// Interactive hover effects
document.addEventListener('DOMContentLoaded', () => {
  // Program highlight hover effects
  document.querySelectorAll('.program-highlight').forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-5px)';
      this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.1)';
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
      this.style.boxShadow = 'var(--shadow-lg)';
    });
  });

  // Highlight card hover effects
  document.querySelectorAll('.highlight-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateX(10px)';
      this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.1)';
    });
    
    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateX(5px)';
      this.style.boxShadow = 'var(--shadow-md)';
    });
  });

  // Button ripple effects
  document.querySelectorAll('.btn').forEach(btn => {
    btn.addEventListener('click', function(e) {
      // Skip ripple for anchor links
      if (this.getAttribute('href') && this.getAttribute('href').startsWith('#')) {
        return;
      }
      
      const ripple = document.createElement('span');
      const rect = this.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;
      
      ripple.style.width = ripple.style.height = size + 'px';
      ripple.style.left = x + 'px';
      ripple.style.top = y + 'px';
      ripple.classList.add('ripple');
      
      this.appendChild(ripple);
      
      setTimeout(() => {
        ripple.remove();
      }, 600);
    });
  });
});

// Mobile menu functionality (if needed)
document.addEventListener('DOMContentLoaded', () => {
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const navMenu = document.querySelector('.nav-menu');
  
  if (mobileMenuToggle && navMenu) {
    mobileMenuToggle.addEventListener('click', () => {
      navMenu.classList.toggle('active');
      mobileMenuToggle.classList.toggle('active');
    });
    
    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
      if (!mobileMenuToggle.contains(e.target) && !navMenu.contains(e.target)) {
        navMenu.classList.remove('active');
        mobileMenuToggle.classList.remove('active');
      }
    });
  }
});

// Lazy loading for images (if implemented)
document.addEventListener('DOMContentLoaded', () => {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            imageObserver.unobserve(img);
          }
        }
      });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img);
    });
  }
});

// Export functions for global use
window.FoundationSite = {
  animateCounter,
  showFormSuccess,
  initializeAnimations,
  initializeCounters
};
